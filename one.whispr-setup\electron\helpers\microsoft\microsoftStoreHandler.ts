import { app } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * Handles Microsoft Store specific logic for first launch
 */
export class MicrosoftStoreHandler {
  private appDataPath: string;
  private embeddedMainAppPath: string;

  constructor() {
    // AppData path where main app should be extracted
    this.appDataPath = path.join(app.getPath('userData'), 'MainApp');
    
    // Path to embedded main app in resources
    this.embeddedMainAppPath = path.join(process.resourcesPath, 'MainApp');
    
    console.log('[MICROSOFT] AppData path:', this.appDataPath);
    console.log('[MICROSOFT] Embedded main app path:', this.embeddedMainAppPath);
  }

  /**
   * Check if this is the first launch (main app not extracted to AppData yet)
   */
  async isFirstLaunch(): Promise<boolean> {
    try {
      // Check if main app exists in AppData
      const mainAppExePath = path.join(this.appDataPath, 'One Whispr.exe');
      const exists = await fs.pathExists(mainAppExePath);
      
      console.log('[MICROSOFT] Main app in AppData exists:', exists);
      return !exists;
    } catch (error) {
      console.error('[MICROSOFT] Error checking first launch:', error);
      return true; // Assume first launch on error
    }
  }

  /**
   * Extract embedded main app to AppData on first launch
   */
  async extractMainAppToAppData(): Promise<boolean> {
    try {
      console.log('[MICROSOFT] Starting main app extraction to AppData...');
      
      // Check if embedded main app exists
      if (!await fs.pathExists(this.embeddedMainAppPath)) {
        console.error('[MICROSOFT] Embedded main app not found:', this.embeddedMainAppPath);
        return false;
      }

      // Ensure AppData directory exists
      await fs.ensureDir(this.appDataPath);

      // Copy entire main app from embedded location to AppData
      console.log('[MICROSOFT] Copying main app from embedded location to AppData...');
      await fs.copy(this.embeddedMainAppPath, this.appDataPath, {
        overwrite: true,
        recursive: true
      });

      // Verify the extraction
      const mainAppExePath = path.join(this.appDataPath, 'One Whispr.exe');
      const extractedSuccessfully = await fs.pathExists(mainAppExePath);
      
      if (extractedSuccessfully) {
        console.log('[MICROSOFT] Main app extracted successfully to AppData');
        return true;
      } else {
        console.error('[MICROSOFT] Main app extraction failed - exe not found after copy');
        return false;
      }
    } catch (error) {
      console.error('[MICROSOFT] Error extracting main app to AppData:', error);
      return false;
    }
  }

  /**
   * Get the path where main app should be launched from (AppData)
   */
  getMainAppPath(): string {
    return path.join(this.appDataPath, 'One Whispr.exe');
  }

  /**
   * Get the backend path in AppData (where runtime should be extracted)
   */
  getBackendPath(): string {
    return path.join(this.appDataPath, 'resources', 'backend');
  }

  /**
   * Get the embedded runtime path (source for extraction)
   */
  getEmbeddedRuntimePath(): string {
    return path.join(this.embeddedMainAppPath, 'resources', 'backend', 'OneWhispr-Runtime-Base.7z');
  }

  /**
   * Check if embedded runtime exists
   */
  async hasEmbeddedRuntime(): Promise<boolean> {
    try {
      const runtimePath = this.getEmbeddedRuntimePath();
      const exists = await fs.pathExists(runtimePath);
      console.log('[MICROSOFT] Embedded runtime exists:', exists, 'at', runtimePath);
      return exists;
    } catch (error) {
      console.error('[MICROSOFT] Error checking embedded runtime:', error);
      return false;
    }
  }

  /**
   * Handle complete first launch setup for Microsoft Store
   */
  async handleFirstLaunch(): Promise<boolean> {
    try {
      console.log('[MICROSOFT] Starting complete first launch setup...');

      // Check if this is actually a first launch
      const isFirst = await this.isFirstLaunch();
      if (!isFirst) {
        console.log('[MICROSOFT] Not a first launch - main app already exists in AppData');
        return true;
      }

      // Extract main app to AppData
      const extractionSuccess = await this.extractMainAppToAppData();
      if (!extractionSuccess) {
        console.error('[MICROSOFT] Failed to extract main app to AppData');
        return false;
      }

      console.log('[MICROSOFT] First launch setup completed successfully');
      return true;
    } catch (error) {
      console.error('[MICROSOFT] Error during first launch setup:', error);
      return false;
    }
  }
}

// Export singleton instance
export const microsoftStoreHandler = new MicrosoftStoreHandler();
