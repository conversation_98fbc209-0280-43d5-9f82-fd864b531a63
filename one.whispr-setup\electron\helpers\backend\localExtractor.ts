import * as fs from 'fs-extra';
import * as path from 'path';
import { getLauncherWindow } from '../../window';
import { ArchiveExtractor } from './archiveExtractor';

interface BackendDownloadProgress {
  type: 'runtime' | 'scripts';
  progress: number;
  speed: number;
  eta: number;
  status: string;
}

/**
 * Handles extraction of local 7z files from one.whispr-app build
 */
export class LocalExtractor {
  private backendPath: string;
  private isExtracting: boolean = false;
  private archiveExtractor: ArchiveExtractor;

  constructor(backendPath: string) {
    this.backendPath = backendPath;
    this.archiveExtractor = new ArchiveExtractor(backendPath);
  }

  /**
   * Check for local 7z files (from one.whispr-app build) and extract them if found
   */
  async checkAndExtractLocal7zFiles(): Promise<{
    extracted: boolean,
    runtimeVersion?: string,
    scriptsVersion?: string,
    reason: string
  }> {
    try {
      // Prevent multiple simultaneous extractions
      if (this.isExtracting) {
        console.log('[BACKEND] Extraction already in progress, skipping...');
        return {
          extracted: false,
          reason: 'Extraction already in progress'
        };
      }

      this.isExtracting = true;

      // Simple check: look for 7z files in the built app's resources/backend directory
      const builtAppPath = path.join(process.cwd(), '..', 'one.whispr-app', '.release', 'win-unpacked');
      const sourceBackendPath = path.join(builtAppPath, 'resources', 'backend');

      const runtimeZipPath = path.join(sourceBackendPath, 'OneWhispr-Runtime-Base.7z');
      const scriptsZipPath = path.join(sourceBackendPath, 'OneWhispr-Scripts.7z');

      console.log('[BACKEND] Checking for local 7z files...');
      console.log('[BACKEND] Source path:', sourceBackendPath);
      console.log('[BACKEND] Runtime 7z exists:', fs.existsSync(runtimeZipPath));
      console.log('[BACKEND] Scripts 7z exists:', fs.existsSync(scriptsZipPath));

      const hasRuntimeZip = fs.existsSync(runtimeZipPath);
      const hasScriptsZip = fs.existsSync(scriptsZipPath);

      if (!hasRuntimeZip && !hasScriptsZip) {
        return {
          extracted: false,
          reason: 'No local 7z files found'
        };
      }

      console.log('[BACKEND] Found local 7z files, will extract both Base.7z and Scripts.7z');

      console.log('[BACKEND] Starting extraction process...');
      this.sendProgress('runtime', 0, 0, 0, 'Found local backend files, starting extraction...');

      // Extract Base.7z to backend/
      if (hasRuntimeZip) {
        console.log('[BACKEND] Extracting Base.7z...');
        this.sendProgress('runtime', 5, 0, 0, 'Starting CUDA runtime extraction...');
        await this.archiveExtractor.extract7z(runtimeZipPath, this.backendPath);
        this.sendProgress('runtime', 100, 0, 0, 'Runtime extraction complete');
        console.log('[BACKEND] Base.7z extracted successfully');
      }

      // Extract Scripts.7z to backend/scripts/
      if (hasScriptsZip) {
        console.log('[BACKEND] Extracting Scripts.7z...');
        this.sendProgress('scripts', 0, 0, 0, 'Extracting backend scripts...');
        const scriptsPath = path.join(this.backendPath, 'scripts');

        // Clean scripts directory first to avoid file conflicts
        if (await fs.pathExists(scriptsPath)) {
          console.log('[BACKEND] Cleaning existing scripts directory...');
          await fs.remove(scriptsPath);
        }
        await fs.ensureDir(scriptsPath);

        await this.archiveExtractor.extract7z(scriptsZipPath, scriptsPath);
        this.sendProgress('scripts', 100, 0, 0, 'Scripts extraction complete');
        console.log('[BACKEND] Scripts.7z extracted successfully');
      }

      // Clean up info files if version files exist
      console.log('[BACKEND] Cleaning up info files...');
      await this.archiveExtractor.cleanupInfoFiles();

      // ONLY AFTER both extractions are complete, delete the 7z files
      console.log('[BACKEND] All extractions complete, cleaning up 7z files...');

      // Small delay to ensure 7zip processes have fully released file handles
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (hasRuntimeZip) {
        console.log('[BACKEND] Deleting Base.7z...');
        try {
          await fs.remove(runtimeZipPath);
        } catch (error) {
          console.warn('[BACKEND] Warning: Could not delete Base.7z:', error);
        }
      }
      if (hasScriptsZip) {
        console.log('[BACKEND] Deleting Scripts.7z...');
        try {
          await fs.remove(scriptsZipPath);
        } catch (error) {
          console.warn('[BACKEND] Warning: Could not delete Scripts.7z:', error);
        }
      }
      console.log('[BACKEND] 7z cleanup complete');

      // Read version information from extracted files
      console.log('[BACKEND] Reading version files from extracted content...');
      let runtimeVersion: string | undefined;
      let scriptsVersion: string | undefined;

      if (hasRuntimeZip) {
        try {
          const runtimeVersionPath = path.join(this.backendPath, 'runtime-version.json');
          console.log('[BACKEND] Looking for runtime version at:', runtimeVersionPath);
          console.log('[BACKEND] Runtime version file exists:', fs.existsSync(runtimeVersionPath));
          if (fs.existsSync(runtimeVersionPath)) {
            const versionData = await fs.readJson(runtimeVersionPath);
            runtimeVersion = versionData.version;
            console.log('[BACKEND] Runtime version found:', runtimeVersion);
          }
        } catch (error) {
          console.warn('[BACKEND] Could not read runtime version from extracted files:', error);
        }
      }

      if (hasScriptsZip) {
        try {
          const scriptsVersionPath = path.join(this.backendPath, 'scripts', 'scripts-version.json');
          console.log('[BACKEND] Looking for scripts version at:', scriptsVersionPath);
          console.log('[BACKEND] Scripts version file exists:', fs.existsSync(scriptsVersionPath));
          if (fs.existsSync(scriptsVersionPath)) {
            const versionData = await fs.readJson(scriptsVersionPath);
            scriptsVersion = versionData.version;
            console.log('[BACKEND] Scripts version found:', scriptsVersion);
          }
        } catch (error) {
          console.warn('[BACKEND] Could not read scripts version from extracted files:', error);
        }
      }

      const result = {
        extracted: true,
        runtimeVersion: runtimeVersion || '1.0.0',
        scriptsVersion: scriptsVersion || '1.0.0',
        reason: `Extracted local 7z files: ${hasRuntimeZip ? 'runtime' : ''}${hasRuntimeZip && hasScriptsZip ? ' + ' : ''}${hasScriptsZip ? 'scripts' : ''}`
      };

      console.log('[BACKEND] Local extraction complete, returning result:', result);
      return result;

    } catch (error) {
      console.error('[BACKEND] Error extracting local 7z files:', error);
      return {
        extracted: false,
        reason: `Error extracting local files: ${error instanceof Error ? error.message : String(error)}`
      };
    } finally {
      // Always reset the extraction flag
      this.isExtracting = false;
    }
  }

  /**
   * Send progress update to renderer
   */
  private sendProgress(type: 'runtime' | 'scripts', progress: number, speed: number, eta: number, status: string): void {
    const launcherWindow = getLauncherWindow();
    if (launcherWindow) {
      launcherWindow.webContents.send('backend:progress', {
        type,
        progress,
        speed,
        eta,
        status
      } as BackendDownloadProgress);
    }
  }
}
