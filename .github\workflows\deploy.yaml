name: Deploy to VPS

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      build_electron:
        description: 'Build Electron App'
        required: false
        default: false
        type: boolean
      rollback_to_version:
        description: 'Rollback to specific version (e.g., 1.2.1)'
        required: false
        type: string
      emergency_rollback:
        description: 'Emergency rollback to previous version'
        required: false
        default: false
        type: boolean

jobs:
  deploy-site:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'one.whispr-site/package-lock.json'

      - name: Install dependencies
        run: |
          cd one.whispr-site
          # Clean install to ensure platform-specific native binaries are correct
          npm ci --prefer-offline --no-audit

      - name: Apply database migrations
        run: |
          cd one.whispr-site
          npx tsx scripts/apply-migrations.ts
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

      - name: Build
        run: |
          cd one.whispr-site
          npm run build
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          NEXT_PUBLIC_SITE_URL: ${{ secrets.NEXT_PUBLIC_SITE_URL }}

      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Deploy to VPS
        run: |
          cd one.whispr-site
          rsync -avz --delete .next package.json package-lock.json public next.config.ts scripts src/migrations ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:${{ secrets.DEPLOY_PATH }}
          
      - name: Deploy utility scripts to VPS
        run: |
          # Deploy rollback script and make it executable
          scp .github/scripts/rollback.sh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:${{ secrets.DEPLOY_PATH }}/scripts/
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "chmod +x ${{ secrets.DEPLOY_PATH }}/scripts/rollback.sh"
          
      - name: Restart application
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd ${{ secrets.DEPLOY_PATH }}
            echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}" > .env.production
            echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}" >> .env.production
            echo "NEXT_PUBLIC_SITE_URL=${{ secrets.NEXT_PUBLIC_SITE_URL }}" >> .env.production
            echo "SUPABASE_SERVICE_ROLE_KEY=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" >> .env.production
            npm install --production
            
            # Run migrations using our improved script
            npx tsx scripts/apply-migrations.ts
            
            pm2 restart one-whispr-site || pm2 start npm --name "one-whispr-site" -- start 

  deploy-app:
    runs-on: windows-latest
    if: github.event.inputs.build_electron == 'true' || contains(github.event.head_commit.message, '[build-electron]')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Needed for git diff

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies for main app
        run: |
          cd one.whispr-app
          npm ci

      - name: Check for Python file changes
        id: python-changes
        run: |
          # Check for different types of Python changes
          $MAIN_WHISPR_CHANGED = $false
          $DEPS_CHANGED = $false

          # Check if main.py or whispr folder changed (quick update)
          $mainWhisprFiles = git diff --name-only ${{ github.event.before }}..${{ github.sha }} | Select-String -Pattern '^one\.whispr-app/python/main\.py$|^one\.whispr-app/python/whispr/'
          if ($mainWhisprFiles) {
            $MAIN_WHISPR_CHANGED = $true
            "main-whispr-changed=true" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            Write-Host "Main.py or whispr folder changed - quick update needed"
          } else {
            "main-whispr-changed=false" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          }

          # Check if dependencies or spec files changed (full rebuild)
          $depsFiles = git diff --name-only ${{ github.event.before }}..${{ github.sha }} | Select-String -Pattern '^one\.whispr-app/python/.*\.spec$|^one\.whispr-app/python/utils/python-setup\.ts$|^one\.whispr-app/python/requirements\.txt$|^one\.whispr-app/package\.json$'
          if ($depsFiles) {
            $DEPS_CHANGED = $true
            "deps-changed=true" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            Write-Host "Dependencies or build configuration changed - full rebuild needed"
          } else {
            "deps-changed=false" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          }

          # Set overall python-changed flag
          if ($MAIN_WHISPR_CHANGED -or $DEPS_CHANGED) {
            "python-changed=true" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            Write-Host "Python files have changed, will rebuild"
          } else {
            "python-changed=false" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            Write-Host "No Python files changed, skipping Python build"
          }
        shell: powershell

      - name: Setup Python environment
        if: steps.python-changes.outputs.python-changed == 'true'
        run: |
          cd one.whispr-app
          # Setup Python environment and dependencies (voice models downloaded on-demand)
          npm run backend-setup

      - name: Install dependencies
        if: steps.python-changes.outputs.python-changed == 'true'
        run: |
          cd one.whispr-app
          npm install
        shell: powershell

      - name: Compile Python backend (Full rebuild)
        if: steps.python-changes.outputs.deps-changed == 'true'
        run: |
          cd one.whispr-app
          # This will download Whisper base model (~277MB) and include it in base runtime
          npm run backend-compile

      - name: Quick update (bytecode compilation only)
        if: steps.python-changes.outputs.main-whispr-changed == 'true' && steps.python-changes.outputs.deps-changed == 'false'
        run: |
          cd one.whispr-app
          npm run backend-compile:quick

      - name: Build main Electron app to individual files
        run: |
          cd one.whispr-app
          npm run build:files

      - name: Install dependencies for setup
        run: |
          cd one.whispr-setup
          npm ci

      - name: Build setup/installer (Direct distribution)
        run: |
          cd one.whispr-setup
          npm run build:files:direct
        env:
          UPDATE_SERVER_URL: https://whispr.one/updates

      - name: Build setup/installer (Microsoft Store)
        run: |
          cd one.whispr-setup
          npm run build:files:microsoft
        env:
          IS_MICROSOFT: true
          UPDATE_SERVER_URL: https://whispr.one/updates

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: onewhispr-packages
          path: |
            one.whispr-setup/.release-direct/squirrel-windows/*
            one.whispr-setup/.release-microsoft/*
            one.whispr-app/.release-direct/win-unpacked/**/*

      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Deploy updates to VPS
        run: |
          # Extract version for directory naming
          cd one.whispr-setup
          $VERSION = node -p "require('./package.json').version"
          cd ..

          # Create directory structure for setup installer
          New-Item -ItemType Directory -Force -Path "updates/setup/versions/$VERSION"
          Copy-Item "one.whispr-setup/.release-direct/squirrel-windows/*" "updates/setup/versions/$VERSION/" -Recurse

          # Copy Microsoft Store package if it exists
          if (Test-Path "one.whispr-setup/.release-microsoft/*.appx") {
            Copy-Item "one.whispr-setup/.release-microsoft/*.appx" "updates/setup/versions/$VERSION/" -Recurse
            Write-Host "Microsoft Store package copied to versions/$VERSION"
          }

          # Create latest setup directory
          New-Item -ItemType Directory -Force -Path "updates/setup/latest"
          Copy-Item "one.whispr-setup/.release-direct/squirrel-windows/*" "updates/setup/latest/" -Recurse

          # Copy Microsoft Store package to latest if it exists
          if (Test-Path "one.whispr-setup/.release-microsoft/*.appx") {
            Copy-Item "one.whispr-setup/.release-microsoft/*.appx" "updates/setup/latest/" -Recurse
            Write-Host "Microsoft Store package copied to latest"
          }

          # Handle different deployment scenarios based on what changed
          if ("${{ steps.python-changes.outputs.deps-changed }}" -eq "true") {
            Write-Host "Full rebuild - deploying base runtime and updatable bytecode"

            # Deploy compressed base runtime (~1.3GB)
            New-Item -ItemType Directory -Force -Path "updates/backend-runtime/versions/$VERSION"
            Copy-Item "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Runtime-Base.7z" "updates/backend-runtime/versions/$VERSION/"

            # Create latest runtime directory
            New-Item -ItemType Directory -Force -Path "updates/backend-runtime/latest"
            Copy-Item "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Runtime-Base.7z" "updates/backend-runtime/latest/"

            # Deploy scripts bytecode (~200KB)
            New-Item -ItemType Directory -Force -Path "updates/backend-scripts/versions/$VERSION"
            Copy-Item "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" "updates/backend-scripts/versions/$VERSION/"

            # Create latest scripts directory
            New-Item -ItemType Directory -Force -Path "updates/backend-scripts/latest"
            Copy-Item "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" "updates/backend-scripts/latest/"

            # Deploy main app (individual files)
            New-Item -ItemType Directory -Force -Path "updates/main-app/versions/$VERSION"
            Copy-Item "one.whispr-app/.release-direct/win-unpacked/*" "updates/main-app/versions/$VERSION/" -Recurse

            # Create latest main app directory
            New-Item -ItemType Directory -Force -Path "updates/main-app/latest"
            Copy-Item "one.whispr-app/.release-direct/win-unpacked/*" "updates/main-app/latest/" -Recurse

            # Mark that both runtime and scripts were deployed
            New-Item -ItemType File -Force -Path "updates/RUNTIME_DEPLOYED"
            New-Item -ItemType File -Force -Path "updates/SCRIPTS_DEPLOYED"

          } elseif ("${{ steps.python-changes.outputs.main-whispr-changed }}" -eq "true") {
            Write-Host "Quick update - deploying only bytecode changes"

            # Deploy updated scripts package (~200KB quick update)
            New-Item -ItemType Directory -Force -Path "updates/backend-scripts/versions/$VERSION"
            Copy-Item "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" "updates/backend-scripts/versions/$VERSION/"

            # Create latest scripts directory
            New-Item -ItemType Directory -Force -Path "updates/backend-scripts/latest"
            Copy-Item "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" "updates/backend-scripts/latest/"

            # Still need to deploy main app for non-Python changes
            New-Item -ItemType Directory -Force -Path "updates/main-app/versions/$VERSION"
            Copy-Item "one.whispr-app/.release-direct/win-unpacked/*" "updates/main-app/versions/$VERSION/" -Recurse

            New-Item -ItemType Directory -Force -Path "updates/main-app/latest"
            Copy-Item "one.whispr-app/.release-direct/win-unpacked/*" "updates/main-app/latest/" -Recurse

            # Mark that only scripts were deployed
            New-Item -ItemType File -Force -Path "updates/SCRIPTS_DEPLOYED"

          } else {
            Write-Host "No Python changes - deploying only main app"

            # Deploy main app only
            New-Item -ItemType Directory -Force -Path "updates/main-app/versions/$VERSION"
            Copy-Item "one.whispr-app/.release-direct/win-unpacked/*" "updates/main-app/versions/$VERSION/" -Recurse

            New-Item -ItemType Directory -Force -Path "updates/main-app/latest"
            Copy-Item "one.whispr-app/.release-direct/win-unpacked/*" "updates/main-app/latest/" -Recurse
          }

          # Create JSON manifest for the setup app to know what to download
          Set-Location "updates/main-app/versions/$VERSION"

          # Get all files and create JSON manifest
          $files = Get-ChildItem -Recurse -File | ForEach-Object {
            $relativePath = $_.FullName.Replace((Get-Location).Path, '.').Replace('\', '/')
            $checksum = (Get-FileHash -Path $_.FullName -Algorithm SHA256).Hash.ToLower()
            $baseUrl = "https://whispr.one/updates/main-app/latest"

            # Remove leading ./ from relative path for URL construction
            $urlPath = $relativePath -replace '^\./', ''

            @{
              path = $relativePath
              size = $_.Length
              checksum = $checksum
              url = "$baseUrl/$urlPath"
            }
          }

          # Calculate total size
          $totalSize = ($files | ForEach-Object { $_.size } | Measure-Object -Sum).Sum

          # Create manifest object
          $manifest = @{
            version = $VERSION
            files = $files
            totalSize = $totalSize
          }

          # Convert to JSON and save
          $manifest | ConvertTo-Json -Depth 3 | Out-File -FilePath "../../../manifest-$VERSION.json" -Encoding UTF8
          Set-Location "../../../../"
          Copy-Item "updates/manifest-$VERSION.json" "updates/main-app/latest/manifest.json"

        shell: powershell

      - name: Create updates directory and transfer files to VPS
        run: |
          # Create updates directory on VPS (in nginx web root)
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "mkdir -p /var/www/html/updates"
          
          # Use SCP to transfer the updates directory
          scp -r updates/* ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:/var/www/html/updates/
        shell: bash

      - name: Update version info and cleanup old versions
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} << 'ENDSSH'
            cd /var/www/html/updates
            
            # Extract version from the uploaded files
            VERSION=$(ls setup/versions/ | sort -V | tail -1)
            
            # Create version.json for setup installer
            cat > setup/version.json << EOF
            {
              "version": "$VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "downloadUrl": "https://whispr.one/updates/setup/latest/OneWhisprSetup.exe",
              "releaseNotes": "Latest version of OneWhispr Setup",
              "deltaUpdatesUrl": "https://whispr.one/updates/setup/versions/$VERSION/"
            }
            EOF
            
            # Create version.json for main app updates
            cat > main-app/version.json << EOF
            {
              "version": "$VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "releaseNotes": "Latest version of One Whispr",
              "downloadUrl": "https://whispr.one/updates/main-app/latest/",
              "manifestUrl": "https://whispr.one/updates/main-app/latest/manifest.json",
              "versionsUrl": "https://whispr.one/updates/main-app/versions/$VERSION/"
            }
            EOF

            # Create version.json for backend runtime (only if deployed in this run)
            if [ -f "RUNTIME_DEPLOYED" ]; then
              echo "Creating backend runtime version file (runtime was deployed)..."
              # Calculate checksum for smart rollback detection
              RUNTIME_CHECKSUM=$(sha256sum backend-runtime/latest/OneWhispr-Runtime-Base.7z | cut -d' ' -f1)
              cat > backend-runtime/runtime-version.json << EOF
            {
              "version": "$VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "releaseNotes": "Backend runtime base package",
              "downloadUrl": "https://whispr.one/updates/backend-runtime/latest/OneWhispr-Runtime-Base.7z",
              "versionsUrl": "https://whispr.one/updates/backend-runtime/versions/$VERSION/",
              "compressionType": "7z-lzma2-ultra",
              "checksum": "$RUNTIME_CHECKSUM"
            }
            EOF
              rm -f "RUNTIME_DEPLOYED"  # Clean up flag
              echo "Created backend-runtime/runtime-version.json"
            else
              echo "Skipping runtime version file (runtime not deployed in this run)"
            fi

            # Create version.json for backend scripts (only if deployed in this run)
            if [ -f "SCRIPTS_DEPLOYED" ]; then
              echo "Creating backend scripts version file (scripts were deployed)..."
              # Calculate checksum for integrity verification
              SCRIPTS_CHECKSUM=$(sha256sum backend-scripts/latest/OneWhispr-Scripts.7z | cut -d' ' -f1)
              cat > backend-scripts/scripts-version.json << EOF
            {
              "version": "$VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "releaseNotes": "Backend scripts bytecode update",
              "downloadUrl": "https://whispr.one/updates/backend-scripts/latest/OneWhispr-Scripts.7z",
              "versionsUrl": "https://whispr.one/updates/backend-scripts/versions/$VERSION/",
              "updateType": "bytecode",
              "compressionType": "7z",
              "checksum": "$SCRIPTS_CHECKSUM"
            }
            EOF
              rm -f "SCRIPTS_DEPLOYED"  # Clean up flag
              echo "Created backend-scripts/scripts-version.json"
            else
              echo "Skipping scripts version file (scripts not deployed in this run)"
            fi
            
            # Keep only last 5 versions (cleanup old ones)
            cd setup/versions
            ls -1 | sort -V | head -n -5 | xargs -r rm -rf
            cd ../../main-app/versions
            ls -1 | sort -V | head -n -5 | xargs -r rm -rf

            # Cleanup backend runtime versions if they exist
            if [ -d "../../backend-runtime/versions" ]; then
              cd ../../backend-runtime/versions
              ls -1 | sort -V | head -n -5 | xargs -r rm -rf
              cd ../../
            fi

            # Cleanup backend scripts versions if they exist
            if [ -d "backend-scripts/versions" ]; then
              cd backend-scripts/versions
              ls -1 | sort -V | head -n -5 | xargs -r rm -rf
              cd ../../
            fi

            echo "Current versions available:"
            echo "Setup versions:"
            ls -la setup/versions/ 2>/dev/null || echo "No setup versions"
            echo "Main app versions:"
            ls -la main-app/versions/ 2>/dev/null || echo "No main app versions"
            echo "Backend runtime versions:"
            ls -la backend-runtime/versions/ 2>/dev/null || echo "No runtime versions"
            echo "Backend scripts versions:"
            ls -la backend-scripts/versions/ 2>/dev/null || echo "No scripts versions"
          ENDSSH
        shell: bash

  rollback:
    runs-on: ubuntu-latest
    if: github.event.inputs.rollback_to_version != '' || github.event.inputs.emergency_rollback == 'true'
    
    steps:
      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Perform rollback
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} << 'ENDSSH'
            cd /var/www/html/updates
            
            # Determine target version
            if [ "${{ github.event.inputs.emergency_rollback }}" = "true" ]; then
              # Emergency: rollback to second-latest version
              TARGET_VERSION=$(ls setup/versions/ | sort -V | tail -2 | head -1)
              echo "EMERGENCY ROLLBACK to version: $TARGET_VERSION"
            elif [ -n "${{ github.event.inputs.rollback_to_version }}" ]; then
              # Specific version rollback
              TARGET_VERSION="${{ github.event.inputs.rollback_to_version }}"
              echo "SELECTIVE ROLLBACK to version: $TARGET_VERSION"
            fi
            
            # Verify target version exists in both channels
            if [ ! -d "setup/versions/$TARGET_VERSION" ] || [ ! -d "main-app/versions/$TARGET_VERSION" ]; then
              echo "ERROR: Version $TARGET_VERSION not found in one or both channels!"
              echo "Available setup versions:"
              ls setup/versions/ || echo "No setup versions found"
              echo "Available main-app versions:"
              ls main-app/versions/ || echo "No main-app versions found"
              exit 1
            fi
            
            # Update setup latest directory
            rm -rf setup/latest/*
            cp setup/versions/$TARGET_VERSION/* setup/latest/
            
            # Update main-app latest directory
            rm -rf main-app/latest/*
            cp -r main-app/versions/$TARGET_VERSION/* main-app/latest/
            cp manifest-$TARGET_VERSION.json main-app/latest/manifest.json

            # Update backend components if they exist for this version
            if [ -d "backend-runtime/versions/$TARGET_VERSION" ]; then
              echo "Rolling back backend runtime to version: $TARGET_VERSION"
              rm -rf backend-runtime/latest/*
              cp -r backend-runtime/versions/$TARGET_VERSION/* backend-runtime/latest/
            fi

            if [ -d "backend-scripts/versions/$TARGET_VERSION" ]; then
              echo "Rolling back backend scripts to version: $TARGET_VERSION"
              rm -rf backend-scripts/latest/*
              cp -r backend-scripts/versions/$TARGET_VERSION/* backend-scripts/latest/
            fi
            
            # Update setup version.json
            cat > setup/version.json << EOF
            {
              "version": "$TARGET_VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "downloadUrl": "https://whispr.one/updates/setup/latest/OneWhisprSetup.exe",
              "releaseNotes": "Rolled back to version $TARGET_VERSION",
              "deltaUpdatesUrl": "https://whispr.one/updates/setup/versions/$TARGET_VERSION/",
              "isRollback": true
            }
            EOF
            
            # Update main-app version.json
            cat > main-app/version.json << EOF
            {
              "version": "$TARGET_VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "releaseNotes": "Rolled back to version $TARGET_VERSION",
              "downloadUrl": "https://whispr.one/updates/main-app/latest/",
              "manifestUrl": "https://whispr.one/updates/main-app/latest/manifest.json",
              "versionsUrl": "https://whispr.one/updates/main-app/versions/$TARGET_VERSION/",
              "isRollback": true
            }
            EOF

            # Update backend runtime version.json if it exists
            if [ -d "backend-runtime/latest" ]; then
              cat > backend-runtime/runtime-version.json << EOF
            {
              "version": "$TARGET_VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "releaseNotes": "Rolled back backend runtime to version $TARGET_VERSION",
              "downloadUrl": "https://whispr.one/updates/backend-runtime/latest/OneWhispr-Runtime-Base.7z",
              "versionsUrl": "https://whispr.one/updates/backend-runtime/versions/$TARGET_VERSION/",
              "compressionType": "7z-lzma2-ultra",
              "isRollback": true
            }
            EOF
            fi

            # Update backend scripts version.json if it exists
            if [ -d "backend-scripts/latest" ]; then
              cat > backend-scripts/scripts-version.json << EOF
            {
              "version": "$TARGET_VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "releaseNotes": "Rolled back backend scripts to version $TARGET_VERSION",
              "downloadUrl": "https://whispr.one/updates/backend-scripts/latest/OneWhispr-Scripts.7z",
              "versionsUrl": "https://whispr.one/updates/backend-scripts/versions/$TARGET_VERSION/",
              "updateType": "bytecode",
              "compressionType": "7z",
              "isRollback": true
            }
            EOF
            fi

            echo "Successfully rolled back all components to version: $TARGET_VERSION"
            echo "Current setup version info:"
            cat setup/version.json
            echo "Current main-app version info:"
            cat main-app/version.json
          ENDSSH