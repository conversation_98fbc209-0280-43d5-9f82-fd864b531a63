{"appId": "one.whispr-app", "productName": "One Whispr", "asar": true, "directories": {"output": ".release-direct"}, "files": [".dist/main/**/*", ".dist/renderer/**/*", "node_modules/**/*", "!node_modules/.cache/**/*", "!python/utils/**/*"], "asarUnpack": ["node_modules/**/*.node"], "extraResources": [{"from": "python/models", "to": "models", "filter": ["**/*"]}, {"from": ".dist/One Whispr Backend", "to": "backend", "filter": ["**/*"]}, {"from": "src/assets/one.whispr-white.png", "to": "one.whispr-white.png"}, {"from": "src/assets/one.whispr-black.png", "to": "one.whispr-black.png"}, {"from": "src/assets/sounds", "to": "sounds"}], "win": {"target": [{"target": "dir", "arch": ["x64"]}], "icon": "src/assets/icon.ico"}, "nsis": false, "squirrelWindows": false}