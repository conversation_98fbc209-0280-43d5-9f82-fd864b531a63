# Create self-signed certificate for local APPX testing
# This script creates a certificate that matches the publisher in electron-builder-microsoft.json

Write-Host "Creating self-signed certificate for One Whispr APPX testing..." -ForegroundColor Green

# Certificate details (must match electron-builder-microsoft.json)
$publisherName = "CN=IjazSadiqBasha"
$certName = "OneWhispr-Dev-Certificate"
$outputPath = ".\dev-certificate"

# Create output directory
if (!(Test-Path $outputPath)) {
    New-Item -ItemType Directory -Path $outputPath -Force
}

# Create self-signed certificate
$cert = New-SelfSignedCertificate `
    -Type Custom `
    -Subject $publisherName `
    -KeyUsage DigitalSignature `
    -FriendlyName $certName `
    -CertStoreLocation "Cert:\CurrentUser\My" `
    -TextExtension @("*********={text}*******.*******.3", "*********={text}")

Write-Host "Certificate created with thumbprint: $($cert.Thumbprint)" -ForegroundColor Yellow

# Export certificate to file
$certPath = "$outputPath\OneWhispr-Dev.cer"
Export-Certificate -Cert $cert -FilePath $certPath -Type CERT

# Export private key (for electron-builder)
$pfxPath = "$outputPath\OneWhispr-Dev.pfx"
$password = ConvertTo-SecureString -String "onewhispr-dev" -Force -AsPlainText
Export-PfxCertificate -Cert $cert -FilePath $pfxPath -Password $password

Write-Host "Certificate exported to:" -ForegroundColor Green
Write-Host "  Public: $certPath" -ForegroundColor White
Write-Host "  Private: $pfxPath" -ForegroundColor White
Write-Host "  Password: onewhispr-dev" -ForegroundColor White

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Install certificate to Trusted People store:" -ForegroundColor White
Write-Host "   Right-click $certPath -> Install Certificate -> Local Machine -> Trusted People" -ForegroundColor Gray
Write-Host "2. Update electron-builder-microsoft.json with certificate path" -ForegroundColor White
Write-Host "3. Build and test your APPX package" -ForegroundColor White

Write-Host "`nCertificate will be valid for 1 year from today." -ForegroundColor Yellow
