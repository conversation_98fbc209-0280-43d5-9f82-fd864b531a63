{"appId": "one.whispr-setup", "productName": "One Whispr", "directories": {"output": ".release-microsoft", "buildResources": "src/assets"}, "files": [".dist/main/**/*", ".dist/renderer/**/*", "node_modules/**/*", "!node_modules/.cache/**/*"], "extraResources": [{"from": "../one.whispr-app/.release/win-unpacked", "to": "MainApp", "filter": ["**/*"]}], "win": {"target": [{"target": "appx", "arch": ["x64"]}], "icon": "src/assets/icon.ico"}, "appx": {"displayName": "One Whispr", "publisherDisplayName": "One Whispr Team", "identityName": "IjazSadiqBasha.OneWhispr", "publisher": "CN=IjazSadiqBasha", "applicationId": "OneWhispr", "backgroundColor": "#1a1a1a", "showNameOnTiles": true, "languages": ["en-US"], "setBuildNumber": true, "addAutoLaunchExtension": false, "electronUpdaterAware": false, "artifactName": "OneWhisprSetup.appx"}}