import * as fs from 'fs-extra';
import * as path from 'path';
import axios from 'axios';
import { getLauncherWindow } from '../../window';
import { BACKEND_UPDATES } from '../../../src/lib/constants';
import { ArchiveExtractor } from './archiveExtractor';
import { VersionManager } from './versionManager';
import { downloader } from '../main-app/mainAppDownloader';

interface BackendDownloadProgress {
  type: 'runtime' | 'scripts';
  progress: number;
  speed: number;
  eta: number;
  status: string;
}

/**
 * Handles downloading of individual backend components
 */
export class ComponentDownloaders {
  private backendPath: string;
  private abortController: AbortController | null = null;
  private archiveExtractor: ArchiveExtractor;
  private versionManager: VersionManager;

  constructor(backendPath: string) {
    this.backendPath = backendPath;
    this.archiveExtractor = new ArchiveExtractor(backendPath);
    this.versionManager = new VersionManager(backendPath);
  }

  setAbortController(controller: AbortController | null): void {
    this.abortController = controller;
  }

  /**
   * Download and extract base runtime
   */
  async downloadRuntime(): Promise<void> {
    const runtimeInfo = await this.versionManager.fetchVersionInfo('runtime');
    if (!runtimeInfo) {
      throw new Error('Failed to fetch runtime version info');
    }
    
    this.sendProgress('runtime', 0, 0, 0, 'Downloading CUDA runtime...');
    
    // Download the 7z archive
    const archivePath = path.join(this.backendPath, 'OneWhispr-Runtime-Base.7z');
    await this.downloadFile(BACKEND_UPDATES.runtime.archiveUrl, archivePath, 'runtime');
    
    this.sendProgress('runtime', 90, 0, 0, 'Extracting CUDA runtime...');

    // Extract the archive
    await this.archiveExtractor.extract7z(archivePath, this.backendPath);

    // Clean up info files if version files exist
    await this.archiveExtractor.cleanupInfoFiles();

    // Clean up archive
    await fs.remove(archivePath);
    
    // Save version info
    await fs.writeJson(path.join(this.backendPath, 'runtime-version.json'), {
      version: runtimeInfo.version,
      releaseDate: runtimeInfo.releaseDate,
      checksum: runtimeInfo.checksum,  // Save checksum for future comparisons
      installedAt: new Date().toISOString()
    });
    
    this.sendProgress('runtime', 100, 0, 0, 'CUDA runtime installed');
  }

  /**
   * Download and extract scripts code
   */
  async downloadScripts(): Promise<void> {
    const scriptsInfo = await this.versionManager.fetchVersionInfo('scripts');
    if (!scriptsInfo) {
      throw new Error('Failed to fetch scripts version info');
    }

    this.sendProgress('scripts', 0, 0, 0, 'Downloading backend scripts...');

    // Create scripts directory
    const scriptsPath = path.join(this.backendPath, 'scripts');

    // Clean up old scripts files first
    await this.cleanupScriptsFiles();

    // Download the 7z archive
    const archivePath = path.join(this.backendPath, 'OneWhispr-Scripts.7z');
    await this.downloadFile(BACKEND_UPDATES.scripts.archiveUrl, archivePath, 'scripts');

    this.sendProgress('scripts', 90, 0, 0, 'Extracting backend scripts...');

    // Extract the archive to scripts directory
    await this.archiveExtractor.extract7z(archivePath, scriptsPath);

    // Clean up info files if version files exist
    await this.archiveExtractor.cleanupInfoFiles();

    // Clean up archive
    await fs.remove(archivePath);

    // Save version info in scripts directory
    await fs.writeJson(path.join(scriptsPath, 'scripts-version.json'), {
      version: scriptsInfo.version,
      releaseDate: scriptsInfo.releaseDate,
      installedAt: new Date().toISOString()
    });

    this.sendProgress('scripts', 100, 0, 0, 'Backend scripts updated');
  }

  /**
   * Download Whisper base model as final installation step
   */
  async downloadWhisperBaseModel(): Promise<void> {
    try {
      console.log('[MODELS] Starting Whisper base model download...');

      // Define Whisper base model files from model_registry.py
      const whisperBaseFiles = [
        { name: 'model.safetensors', size: 290403936, url: 'https://huggingface.co/openai/whisper-base/resolve/main/model.safetensors' },
        { name: 'config.json', size: 1983, url: 'https://huggingface.co/openai/whisper-base/resolve/main/config.json' },
        { name: 'tokenizer.json', size: 2480466, url: 'https://huggingface.co/openai/whisper-base/resolve/main/tokenizer.json' },
        { name: 'generation_config.json', size: 3807, url: 'https://huggingface.co/openai/whisper-base/resolve/main/generation_config.json' },
        { name: 'tokenizer_config.json', size: 282683, url: 'https://huggingface.co/openai/whisper-base/resolve/main/tokenizer_config.json' },
        { name: 'preprocessor_config.json', size: 184990, url: 'https://huggingface.co/openai/whisper-base/resolve/main/preprocessor_config.json' },
        { name: 'added_tokens.json', size: 34604, url: 'https://huggingface.co/openai/whisper-base/resolve/main/added_tokens.json' },
        { name: 'normalizer.json', size: 52666, url: 'https://huggingface.co/openai/whisper-base/resolve/main/normalizer.json' },
        { name: 'merges.txt', size: 493869, url: 'https://huggingface.co/openai/whisper-base/resolve/main/merges.txt' },
        { name: 'special_tokens_map.json', size: 2194, url: 'https://huggingface.co/openai/whisper-base/resolve/main/special_tokens_map.json' },
        { name: 'vocab.json', size: 835550, url: 'https://huggingface.co/openai/whisper-base/resolve/main/vocab.json' }
      ];

      // Check if model already exists and is complete
      // In development mode, use the local build path; in production, use AppData
      const { app } = require('electron');
      let mainAppPath: string;

      if (!app.isPackaged) {
        // Development mode: use local build directory
        mainAppPath = path.join(process.cwd(), '..', 'one.whispr-app', '.release', 'win-unpacked');
      } else {
        // Production mode: use AppData
        mainAppPath = downloader.getDownloadPath();
      }

      const voiceModelsPath = path.join(mainAppPath, 'resources', 'voicemodels');
      const whisperBasePath = path.join(voiceModelsPath, 'whisper-base');

      // Check if all required files exist with correct sizes
      if (fs.existsSync(whisperBasePath)) {
        console.log('[MODELS] Checking existing Whisper base model...');

        let allFilesValid = true;
        let existingFiles = 0;

        for (const file of whisperBaseFiles) {
          const filePath = path.join(whisperBasePath, file.name);
          if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            if (stats.size === file.size) {
              existingFiles++;
            } else {
              console.log(`[MODELS] File ${file.name} has incorrect size (${stats.size} vs ${file.size})`);
              allFilesValid = false;
              break;
            }
          } else {
            console.log(`[MODELS] Missing file: ${file.name}`);
            allFilesValid = false;
            break;
          }
        }

        if (allFilesValid && existingFiles === whisperBaseFiles.length) {
          console.log(`[MODELS] Whisper base model is complete (${existingFiles}/${whisperBaseFiles.length} files), skipping download`);
          this.sendProgress('scripts', 100, 0, 0, 'Whisper base model already available');
          return;
        } else {
          console.log(`[MODELS] Whisper base model incomplete (${existingFiles}/${whisperBaseFiles.length} files valid), will download missing files`);
        }
      } else {
        console.log('[MODELS] Whisper base model not found, starting fresh download');
      }

      this.sendProgress('scripts', 0, 0, 0, 'Downloading Whisper base model...');

      // Ensure voicemodels directory exists
      await fs.ensureDir(whisperBasePath);

      const totalSize = whisperBaseFiles.reduce((sum, file) => sum + file.size, 0);
      let downloadedSize = 0;

      console.log(`[MODELS] Downloading ${whisperBaseFiles.length} files (${Math.round(totalSize / 1024 / 1024)}MB)...`);

      // Download each file
      for (let i = 0; i < whisperBaseFiles.length; i++) {
        const file = whisperBaseFiles[i];
        const filePath = path.join(whisperBasePath, file.name);

        // Skip if file already exists and has correct size
        if (fs.existsSync(filePath)) {
          const stats = fs.statSync(filePath);
          if (stats.size === file.size) {
            console.log(`[MODELS] File ${file.name} already exists with correct size`);
            downloadedSize += file.size;
            continue;
          }
        }

        console.log(`[MODELS] Downloading ${file.name} (${Math.round(file.size / 1024 / 1024)}MB)...`);

        await this.downloadFile(file.url, filePath, 'scripts');
        downloadedSize += file.size;
      }

      this.sendProgress('scripts', 100, 0, 0, 'Whisper base model ready');
      console.log('[MODELS] Whisper base model download completed successfully');

    } catch (error) {
      console.error('[MODELS] Error downloading Whisper base model:', error);
      // Don't throw - this is not critical for app functionality
      this.sendProgress('scripts', 0, 0, 0, 'Whisper model download failed (will download on first use)');
    }
  }

  /**
   * Clean up old scripts files
   */
  private async cleanupScriptsFiles(): Promise<void> {
    try {
      const scriptsPath = path.join(this.backendPath, 'scripts');

      // Remove the entire scripts directory if it exists
      if (fs.existsSync(scriptsPath)) {
        await fs.remove(scriptsPath);
        console.log('[BACKEND] Cleaned up old scripts directory');
      }

      // Ensure the scripts directory exists
      await fs.ensureDir(scriptsPath);
    } catch (error) {
      console.warn('[BACKEND] Error cleaning up scripts files:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Download a file with progress reporting
   */
  private async downloadFile(url: string, filePath: string, type: 'runtime' | 'scripts'): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log(`[BACKEND] Downloading ${type} from ${url}`);

        // Ensure directory exists
        await fs.ensureDir(path.dirname(filePath));

        // Create write stream
        const writer = fs.createWriteStream(filePath);

        // Track progress
        let downloadedBytes = 0;
        const startTime = Date.now();
        let lastUpdateTime = startTime;
        let lastBytes = 0;
        let speed = 0;

        // Download the file
        const response = await axios({
          method: 'get',
          url,
          responseType: 'stream',
          signal: this.abortController?.signal,
          timeout: 300000, // 5 minute timeout for large files
          headers: { 'User-Agent': 'OneWhispr-Setup/1.0.0' }
        });

        const totalSize = parseInt(response.headers['content-length'] || '0');

        response.data.on('data', (chunk: Buffer) => {
          downloadedBytes += chunk.length;

          // Calculate progress
          const progress = totalSize > 0 ? Math.min(90, Math.round((downloadedBytes / totalSize) * 90)) : 0;

          // Calculate speed and ETA
          const now = Date.now();
          const timeDiff = (now - lastUpdateTime) / 1000;

          if (timeDiff >= 1) { // Update every second
            speed = Math.round((downloadedBytes - lastBytes) / timeDiff);
            lastBytes = downloadedBytes;
            lastUpdateTime = now;
          }

          // Calculate ETA
          const remainingBytes = totalSize - downloadedBytes;
          const eta = speed > 0 ? Math.round(remainingBytes / speed) : 0;

          // Send progress update
          this.sendProgress(type, progress, speed, eta, `Downloading ${type}...`);
        });

        // Handle completion
        response.data.pipe(writer);

        writer.on('finish', resolve);
        writer.on('error', reject);
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Send progress update to renderer
   */
  private sendProgress(type: 'runtime' | 'scripts', progress: number, speed: number, eta: number, status: string): void {
    const launcherWindow = getLauncherWindow();
    if (launcherWindow) {
      launcherWindow.webContents.send('backend:progress', {
        type,
        progress,
        speed,
        eta,
        status
      } as BackendDownloadProgress);
    }
  }
}
